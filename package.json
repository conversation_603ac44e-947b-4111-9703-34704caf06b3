{"name": "next_project_template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "lint-staged": "lint-staged", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@tanstack/react-query": "^5.85.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "konva": "^10.0.2", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^15.7.3", "react-konva": "^19.0.10", "tailwind-merge": "^3.3.1", "use-image": "^1.1.4", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@storybook/addon-a11y": "^9.1.5", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-onboarding": "^9.1.5", "@storybook/addon-vitest": "^9.1.5", "@storybook/nextjs-vite": "^9.1.5", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.34.0", "eslint-config-next": "15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^9.1.5", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.6", "playwright": "^1.55.0", "prettier": "^3.6.2", "storybook": "^9.1.5", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}}