// src/pages/MetricDisplayDemo.tsx
import React from 'react';
import MetricDisplay from '@/components/common/ui/MetricDisplay';
import AlertBadge from '@/components/common/ui/AlertBadge';

export default function MetricDisplayDemo() {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
            <div className="max-w-6xl mx-auto">
                {/* العنوان الرئيسي */}
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Fire alarm system</h1>

                {/* شبكة البطاقات */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* بطاقة Total devices */}
                    <div className="bg-gray-800  rounded-lg shadow-md p-6">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Total devices</h2>
                        <div className="space-y-4">
                            <MetricDisplay label="Active Alarms" value={200} isAlert={true} size="lg" />
                        </div>
                    </div>

                    {/* بطاقة Access control */}
                    <div className="bg-gray-700 dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Access control</h2>
                        <div className="space-y-4">
                            <MetricDisplay label="Total doors" value={150} size="md" />
                            <MetricDisplay label="Open/Closed" value="12/138" layout="vertical" highlight={true} />
                        </div>
                    </div>

                    {/* بطاقة CCTV control */}
                    <div className=" bg-gray-700 rounded-lg shadow-md p-6">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">CCTV control</h2>
                        <div className="space-y-4">
                            <MetricDisplay label="Total cameras" value={200} size="md" />
                            <MetricDisplay label="Active Incidents" value={5} isAlert={true} layout="vertical" />
                        </div>
                    </div>

                    {/* بطاقة Gate Barriers */}
                    <div className="bg-gray-700 rounded-lg shadow-md p-6">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Gate Barriers</h2>
                        <div className="space-y-4">
                            <MetricDisplay label="Total Barriers" value={8} size="md" />
                            <MetricDisplay label="Operational" value="7/8" layout="vertical" highlight={true} />
                        </div>
                    </div>
                </div>

                {/* قسم إضافي لإظهار جميع حالات المكونات */}
                <div className="mt-12 bg-gray-700 rounded-lg shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-6">Component States Demo</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* AlertBadge States */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                AlertBadge Component
                            </h3>
                            <div className="space-y-3">
                                <div className="flex items-center space-x-4">
                                    <span className="w-32 text-gray-600 dark:text-gray-400">Small:</span>
                                    <AlertBadge value={3} isAlert size="sm" />
                                </div>
                                <div className="flex items-center space-x-4">
                                    <span className="w-32 text-gray-600 dark:text-gray-400">Medium:</span>
                                    <AlertBadge value={5} isAlert size="md" />
                                </div>
                                <div className="flex items-center space-x-4">
                                    <span className="w-32 text-gray-600 dark:text-gray-400">Large:</span>
                                    <AlertBadge value={8} isAlert size="lg" />
                                </div>
                                <div className="flex items-center space-x-4">
                                    <span className="w-32 text-gray-600 dark:text-gray-400">Normal:</span>
                                    <AlertBadge value={12} size="md" />
                                </div>
                            </div>
                        </div>

                        {/* MetricDisplay States */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                MetricDisplay Component
                            </h3>
                            <div className="space-y-4">
                                <MetricDisplay label="Horizontal Layout" value={42} layout="horizontal" />
                                <MetricDisplay label="Vertical Layout" value={42} layout="vertical" />
                                <MetricDisplay label="With Alert" value={7} isAlert={true} />
                                <MetricDisplay label="Highlighted" value={15} highlight={true} />
                                <MetricDisplay label="Small Size" value={9} size="sm" />
                                <MetricDisplay label="Large Size" value={23} size="lg" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
