// src/components/ui/MetricDisplay/MetricDisplay.tsx
import React from 'react';
import clsx from 'clsx';
import AlertBadge from './AlertBadge';

export interface MetricDisplayProps {
    label: string;
    value: number | string;
    layout?: 'horizontal' | 'vertical';
    isAlert?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
    labelClassName?: string;
    valueClassName?: string;
    valueAlign?: 'start' | 'center' | 'end';
}

export default function MetricDisplay({
    label,
    value,
    layout = 'horizontal',
    isAlert = false,
    size = 'md',
    className,
    labelClassName,
    valueClassName,
}: MetricDisplayProps) {
    const sizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
    };

    const valueClasses = clsx('text-white font-semibold', sizeClasses[size], valueClassName);

    const labelClasses = clsx('text-gray-600  p-4', sizeClasses[size], labelClassName);

    const containerClasses = clsx(
        layout === 'horizontal' ? 'flex justify-between items-center gap-x-2' : 'flex flex-col items-center gap-y-1',
        className,
    );

    return (
        <div className={containerClasses}>
            <span className={labelClasses}>{label}</span>
            {isAlert ? <AlertBadge value={value} isAlert size={size} /> : <span className={valueClasses}>{value}</span>}
        </div>
    );
}
