import React from 'react';
import clsx from 'clsx';

export interface AlertBadgeProps {
    value: number | string;
    isAlert?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export default function AlertBadge({ value, isAlert = false, size = 'md', className }: AlertBadgeProps) {
    const sizeClasses = {
        sm: 'text-sm px-2 py-0.5',
        md: 'text-base px-4 py-1',
        lg: 'text-lg px-8 py-1.5',
    };

    return (
        <span
            className={clsx(
                'inline-flex items-center justify-center rounded-md font-medium',
                'transition-colors duration-200',
                sizeClasses[size],
                isAlert ? 'text-red-700' : 'text-gray-600',
                className,
            )}>
            {value}
        </span>
    );
}
