import React from 'react';
import { render, screen } from '@testing-library/react';
import SystemOverview from './SystemOverview';
import { useSystemsStore } from '@/stores/system.store';

// Mock the systems store
jest.mock('@/stores/system.store');
const mockUseSystemsStore = useSystemsStore as jest.MockedFunction<typeof useSystemsStore>;

// Mock SystemCard component
jest.mock('../SystemCard/SystemCard', () => {
    return function MockSystemCard({ title, iconName, iconColor, metrics }: any) {
        return (
            <div data-testid="system-card">
                <h3>{title}</h3>
                <div data-testid="icon-name">{iconName}</div>
                <div data-testid="icon-color">{iconColor}</div>
                <div data-testid="metrics">{JSON.stringify(metrics)}</div>
            </div>
        );
    };
});

describe('SystemOverview', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders loading state', () => {
        mockUseSystemsStore.mockReturnValue({
            systems: [],
            isLoading: true,
            loadSystems: jest.fn(),
        });

        render(<SystemOverview />);
        expect(screen.getByText('Loading systems...')).toBeInTheDocument();
    });

    it('renders empty state when no systems', () => {
        mockUseSystemsStore.mockReturnValue({
            systems: [],
            isLoading: false,
            loadSystems: jest.fn(),
        });

        render(<SystemOverview />);
        expect(screen.getByText('No systems available')).toBeInTheDocument();
    });

    it('renders systems correctly', () => {
        const mockSystems = [
            {
                title: 'Fire alarm system',
                iconName: 'fire',
                iconColor: '#E87027',
                metrics: [
                    { key: 'Total devices', value: '200', isAlert: false },
                    { key: 'Active Alarms', value: '2', isAlert: true },
                ],
            },
            {
                title: 'Access control',
                iconName: 'door',
                iconColor: '#10BCAD',
                metrics: [
                    { key: 'Total doors', value: '150', isAlert: false },
                    { key: 'Open/Closed', value: '6/144', isAlert: false },
                ],
            },
        ];

        mockUseSystemsStore.mockReturnValue({
            systems: mockSystems,
            isLoading: false,
            loadSystems: jest.fn(),
        });

        render(<SystemOverview />);
        
        expect(screen.getByText('Fire alarm system')).toBeInTheDocument();
        expect(screen.getByText('Access control')).toBeInTheDocument();
        expect(screen.getAllByTestId('system-card')).toHaveLength(2);
    });

    it('calls loadSystems on mount', () => {
        const mockLoadSystems = jest.fn();
        mockUseSystemsStore.mockReturnValue({
            systems: [],
            isLoading: false,
            loadSystems: mockLoadSystems,
        });

        render(<SystemOverview />);
        expect(mockLoadSystems).toHaveBeenCalledTimes(1);
    });
});
