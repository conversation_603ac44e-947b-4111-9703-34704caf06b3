'use client';

import React, { useEffect } from 'react';
import { useSystemsStore } from '@/stores/system.store';
import SystemCard from '../SystemCard/SystemCard';
import { cn } from '@/shared/utils';

interface SystemOverviewProps {
    className?: string;
}

/**
 * SystemOverview Component
 * Displays all systems using SystemCard components
 * Data is fetched from the systems store
 */
function SystemOverview({ className }: SystemOverviewProps) {
    const { systems, isLoading, loadSystems } = useSystemsStore();

    // Load systems data on component mount
    useEffect(() => {
        loadSystems();
    }, [loadSystems]);

    if (isLoading) {
        return (
            <div className={cn('flex items-center justify-center p-8', className)}>
                <div className="text-gray-400">Loading systems...</div>
            </div>
        );
    }

    if (systems.length === 0) {
        return (
            <div className={cn('flex items-center justify-center p-8', className)}>
                <div className="text-gray-400">No systems available</div>
            </div>
        );
    }

    return (
        <div className={cn('', className)}>
            <div className="text-white font-poppins font-bold text-[18px] leading-[100%] mb-3">System Overview</div>
            <div className="space-y-2 max-h-[60vh] overflow-y-auto">
                {systems.map((system, index) => (
                    <SystemCard
                        key={`${system.title}-${index}`}
                        title={system.title}
                        iconName={system.iconName}
                        iconColor={system.iconColor}
                        metrics={system.metrics}
                    />
                ))}
            </div>
        </div>
    );
}

export default SystemOverview;
