import type { Meta, StoryObj } from '@storybook/react';
import RightDrawer from './RightDrawer';

const meta: Meta<typeof RightDrawer> = {
    title: 'Features/AlertDashboard/RightDrawer',
    component: RightDrawer,
    parameters: {
        layout: 'fullscreen',
        backgrounds: {
            default: 'dark',
            values: [
                { name: 'dark', value: '#0d131f' },
                { name: 'light', value: '#ffffff' },
            ],
        },
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Open: Story = {
    args: {
        isOpen: true,
        onToggle: () => console.log('Toggle drawer'),
    },
    decorators: [
        (Story) => (
            <div className="h-screen w-80">
                <Story />
            </div>
        ),
    ],
};

export const Closed: Story = {
    args: {
        isOpen: false,
        onToggle: () => console.log('Toggle drawer'),
    },
    decorators: [
        (Story) => (
            <div className="h-screen w-80">
                <Story />
            </div>
        ),
    ],
};

export const InDashboardContext: Story = {
    args: {
        isOpen: true,
        onToggle: () => console.log('Toggle drawer'),
    },
    decorators: [
        (Story) => (
            <div className="h-screen flex">
                <div className="flex-1 bg-gray-800 p-4">
                    <div className="text-white">Main Dashboard Content</div>
                </div>
                <div className="w-80">
                    <Story />
                </div>
            </div>
        ),
    ],
};
