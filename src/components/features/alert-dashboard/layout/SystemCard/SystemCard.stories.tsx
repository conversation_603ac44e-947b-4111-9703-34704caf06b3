import type { Meta, StoryObj } from '@storybook/react';
import SystemCard from './SystemCard';

const meta: Meta<typeof SystemCard> = {
    title: 'Features/Alert Dashboard/SystemCard',
    component: SystemCard,
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component: 'A reusable system card component that displays system information with metrics and alerts.',
            },
        },
    },
    argTypes: {
        title: {
            control: 'text',
            description: 'The title of the system card',
        },
        iconName: {
            control: 'select',
            options: ['fire', 'door', 'camera', 'gateBarrier', 'building', 'bell'],
            description: 'SVG icon name from public/svg/',
        },
        icon: {
            control: false,
            description: 'Fallback icon component or emoji string',
        },
        isLoading: {
            control: 'boolean',
            description: 'Shows loading skeleton when true',
        },
        onCardClick: {
            action: 'clicked',
            description: 'Callback function when card is clicked',
        },
    },
};

export default meta;
type Story = StoryObj<typeof SystemCard>;

// Fire Alarm System Story
export const FireAlarmSystem: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        primaryMetric: {
            label: 'Total Devices',
            value: 42,
        },
        secondaryMetrics: [
            {
                label: 'Active Alarms',
                value: 5,
                isAlert: true,
            },
        ],
    },
};

// Access Control System Story
export const AccessControlSystem: Story = {
    args: {
        title: 'Access Control',
        iconName: 'door',
        primaryMetric: {
            label: 'Total Doors',
            value: 28,
        },
        secondaryMetrics: [
            {
                label: 'Open',
                value: 12,
                highlight: true,
            },
            {
                label: 'Closed',
                value: 16,
            },
        ],
    },
};

// CCTV System Story
export const CCTVSystem: Story = {
    args: {
        title: 'CCTV System',
        iconName: 'camera',
        primaryMetric: {
            label: 'Total Cameras',
            value: 156,
        },
        secondaryMetrics: [
            {
                label: 'Active Incidents',
                value: 2,
                isAlert: true,
            },
        ],
    },
};

// Gate Barriers System Story
export const GateBarriersSystem: Story = {
    args: {
        title: 'Gate Barriers',
        iconName: 'gateBarrier',
        primaryMetric: {
            label: 'Total Barriers',
            value: 8,
        },
        secondaryMetrics: [
            {
                label: 'Unauthorized Attempts',
                value: 3,
                isAlert: true,
            },
        ],
    },
};

// With Emoji Icons
export const WithEmojiIcons: Story = {
    args: {
        title: 'Fire Alarm System',
        icon: '🔥',
        primaryMetric: {
            label: 'Total Devices',
            value: 42,
        },
        secondaryMetrics: [
            {
                label: 'Active Alarms',
                value: 5,
                isAlert: true,
            },
        ],
    },
};

// Loading State
export const LoadingState: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        primaryMetric: {
            label: 'Total Devices',
            value: 42,
        },
        secondaryMetrics: [
            {
                label: 'Active Alarms',
                value: 5,
                isAlert: true,
            },
        ],
        isLoading: true,
    },
};

// Interactive Card
export const InteractiveCard: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        primaryMetric: {
            label: 'Total Devices',
            value: 42,
        },
        secondaryMetrics: [
            {
                label: 'Active Alarms',
                value: 5,
                isAlert: true,
            },
        ],
        onCardClick: () => alert('Card clicked!'),
    },
};

// All Systems Grid - Mixed Icons (SVG + Emoji)
export const AllSystemsGrid: Story = {
    render: () => (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl">
            <SystemCard
                title="Fire Alarm System"
                iconName="fire"
                primaryMetric={{ label: 'Total Devices', value: 42 }}
                secondaryMetrics={[{ label: 'Active Alarms', value: 5, isAlert: true }]}
            />
            <SystemCard
                title="Access Control"
                iconName="door"
                primaryMetric={{ label: 'Total Doors', value: 28 }}
                secondaryMetrics={[
                    { label: 'Open', value: 12, highlight: true },
                    { label: 'Closed', value: 16 },
                ]}
            />
            <SystemCard
                title="CCTV System"
                iconName="camera"
                primaryMetric={{ label: 'Total Cameras', value: 156 }}
                secondaryMetrics={[{ label: 'Active Incidents', value: 2, isAlert: true }]}
            />
            <SystemCard
                title="Gate Barriers"
                iconName="gateBarrier"
                primaryMetric={{ label: 'Total Barriers', value: 8 }}
                secondaryMetrics={[{ label: 'Unauthorized Attempts', value: 3, isAlert: true }]}
            />
        </div>
    ),
};

// Building Management System - Additional Example
export const BuildingManagement: Story = {
    args: {
        title: 'Building Management',
        iconName: 'building',
        primaryMetric: {
            label: 'Total Floors',
            value: 25,
        },
        secondaryMetrics: [
            {
                label: 'Occupied',
                value: 23,
                highlight: true,
            },
            {
                label: 'Maintenance',
                value: 2,
                isAlert: true,
            },
        ],
    },
};
