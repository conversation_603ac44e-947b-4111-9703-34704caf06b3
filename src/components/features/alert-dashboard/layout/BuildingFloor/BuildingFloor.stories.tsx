import type { Meta, StoryObj } from '@storybook/react';
import BuildingFloor from './BuildingFloor';

const meta: Meta<typeof BuildingFloor> = {
    title: 'Features/AlertDashboard/BuildingFloor',
    component: BuildingFloor,
    parameters: {
        layout: 'padded',
        backgrounds: {
            default: 'dark',
            values: [
                { name: 'dark', value: '#0d131f' },
                { name: 'light', value: '#ffffff' },
            ],
        },
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        className: 'max-w-md',
    },
};

export const InDrawerContext: Story = {
    args: {},
    decorators: [
        (Story) => (
            <div className="bg-[#0d131ff2] p-4 min-h-screen">
                <Story />
            </div>
        ),
    ],
};
