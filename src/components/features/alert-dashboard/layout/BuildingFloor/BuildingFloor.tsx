'use client';

import React, { useState } from 'react';
import { cn } from '@/shared/utils';

interface FloorItem {
    id: number;
    name: string;
    hasAlert: boolean;
}

interface BuildingFloorProps {
    className?: string;
}

/**
 * BuildingFloor Component
 * Displays building floors with alert indicators
 */
function BuildingFloor({ className }: BuildingFloorProps) {
    const [isExpanded, setIsExpanded] = useState(true);
    
    // Mock data for floors - you can replace this with real data from store
    const floors: FloorItem[] = [
        { id: 1, name: 'Floor 1', hasAlert: true },
        { id: 2, name: 'Floor 2', hasAlert: true },
        { id: 3, name: 'Floor 3', hasAlert: false },
        { id: 4, name: 'Floor 4', hasAlert: false },
        { id: 5, name: 'Floor 5', hasAlert: true },
    ];

    return (
        <div className={cn('', className)}>
            {/* Header */}
            <div 
                className="flex items-center justify-between cursor-pointer mb-3"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <h3 className="text-white font-poppins font-bold text-[18px] leading-[100%]">
                    Building floor
                </h3>
                <div className="flex items-center gap-2">
                    <span className="text-gray-400 text-sm">Building A</span>
                    <svg 
                        className={cn(
                            "w-4 h-4 text-gray-400 transition-transform duration-200",
                            isExpanded ? "rotate-180" : ""
                        )}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Floor List */}
            {isExpanded && (
                <div className="space-y-2">
                    {floors.map((floor) => (
                        <div 
                            key={floor.id}
                            className="flex items-center justify-between p-3 bg-[#212633] rounded-lg hover:bg-[#2a2f3d] transition-colors cursor-pointer"
                        >
                            <span className="text-gray-200 text-sm font-medium">
                                {floor.name}
                            </span>
                            {floor.hasAlert && (
                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}

export default BuildingFloor;
