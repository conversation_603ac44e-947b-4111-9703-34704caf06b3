'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';
import SystemOverview from './SystemOverview';
import BuildingFloor from './BuildingFloor';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard
 */
function RightDrawer({ isOpen = true, onToggle, className }: RightDrawerProps) {
    return (
        <div
            className={`bg-[#0d131ff2] transition-all duration-300 relative ${className || ''}`}
            style={{ gridArea: 'drawer' }}>
            {/* Toggle Button - shows when drawer is closed */}
            {!isOpen && (
                <div className="absolute right-0 top-1/5 transform -translate-y-1/2 -translate-x-full">
                    <button
                        onClick={onToggle}
                        className="bg-[#0d131ff2] border border-gray-600 text-gray-300 p-2 rounded-l hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Open drawer">
                        ⚙️
                    </button>
                </div>
            )}

            {/* Drawer Content - shows when drawer is open */}
            {isOpen && (
                <div className="h-full flex flex-col">
                    {/* Close Button */}
                    <div className="absolute right-8 top-3">
                        <button
                            onClick={onToggle}
                            className="bg-transparent border-none text-gray-300 text-xl cursor-pointer p-1 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                            aria-label="Close drawer">
                            ✕
                        </button>
                    </div>

                    {/* Content */}
                    <div className="flex-1 flex flex-col overflow-hidden">
                        {/* SystemOverview Section */}
                        <div className="flex-shrink-0 p-4 pb-2">
                            <SystemOverview />
                        </div>

                        {/* Divider */}
                        <div className="flex-shrink-0 px-4">
                            <div className="h-px bg-gray-600/50"></div>
                        </div>

                        {/* BuildingFloor Section */}
                        <div className="flex-1 p-4 pt-4 overflow-y-auto">
                            <BuildingFloor />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default RightDrawer;
