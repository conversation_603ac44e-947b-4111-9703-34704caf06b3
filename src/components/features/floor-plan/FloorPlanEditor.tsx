'use client';

import React, { useState, useRef } from 'react';
import { MarkerSelector } from './MarkerSelector';
import { PlacedMarkerComponent } from './PlacedMarkerComponent';
import { CoordinateDisplay } from './CoordinateDisplay';
import { Marker } from '../../../infrastructure/api/markers/types';
import { MarkerType, PlacedMarker } from './types';
import { getMarkerZoneFormat } from '../../../infrastructure/api/markers/zone-formatter';

export function FloorPlanEditor() {
  const [selectedMarkerType, setSelectedMarkerType] = useState<MarkerType | null>(null);
  const [placedMarkers, setPlacedMarkers] = useState<PlacedMarker[]>([]);
  const [currentCoordinates, setCurrentCoordinates] = useState<{ x: number; y: number } | null>(null);
  const [buildingId, setBuildingId] = useState<number>(1);
  const [floorId, setFloorId] = useState<number>(1);
  const [zoneId, setZoneId] = useState<number>(1);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!selectedMarkerType || !imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Calculate percentage coordinates
    const percentageX = (x / rect.width) * 100;
    const percentageY = (y / rect.height) * 100;

    const newMarker: PlacedMarker = {
      id: Date.now().toString(),
      name: `${selectedMarkerType.charAt(0).toUpperCase() + selectedMarkerType.slice(1)} ${placedMarkers.length + 1}`,
      type: selectedMarkerType,
      position_x: percentageX,
      position_y: percentageY,
      exact_x: x,
      exact_y: y,
      status: 'normal',
      isAlert: false,
      floorId: floorId.toString(),
      zoneId: `zone-${zoneId}`,
      buildingId: buildingId.toString(),
      description: `${selectedMarkerType} marker placed at ${x.toFixed(0)}px, ${y.toFixed(0)}px`,
      lastUpdated: new Date().toISOString(),
      metadata: {
        placedBy: 'user',
        placedAt: new Date().toISOString()
      }
    };

    setPlacedMarkers(prev => [...prev, newMarker]);
  };

  const handleImageMouseMove = (event: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setCurrentCoordinates({ x, y });
  };

  const handleImageMouseLeave = () => {
    setCurrentCoordinates(null);
  };

  const removeMarker = (markerId: string | number) => {
    setPlacedMarkers(prev => prev.filter(marker => marker.id !== markerId));
  };

  const clearAllMarkers = () => {
    if (confirm('Are you sure you want to remove all markers?')) {
      setPlacedMarkers([]);
    }
  };

  const exportCoordinates = () => {
    const exportData = {
      floorPlan: '/plans/floorPlan-b1-f1.png',
      buildingId: buildingId,
      floorId: floorId,
      zoneId: zoneId,
      markers: placedMarkers.map(marker => ({
        id: marker.id,
        name: marker.name,
        type: marker.type,
        position_x: Math.round(marker.exact_x),
        position_y: Math.round(marker.exact_y),
        status: marker.status,
        isAlert: marker.isAlert,
        floorId: parseInt(marker.floorId),
        zoneId: marker.zoneId,
        buildingId: parseInt(marker.buildingId),
        description: marker.description,
        lastUpdated: marker.lastUpdated,
        metadata: marker.metadata
      }))
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `floor-plan-markers-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex h-screen">
      {/* Left Panel - Compact Icon Toolbar */}
      <div className="w-20 bg-white border-r border-gray-200 p-2 flex flex-col items-center">
        <div className="mb-4">
          <h3 className="text-xs font-semibold text-gray-600 mb-2 text-center">Tools</h3>
          <MarkerSelector
            selectedMarkerType={selectedMarkerType}
            onMarkerTypeChange={setSelectedMarkerType}
          />
        </div>
        
        {/* Compact Input Fields */}
        <div className="space-y-3">
          <div className="flex flex-col items-center">
            <label className="text-xs font-medium text-gray-700 mb-1">B</label>
            <input
              type="number"
              min="1"
              value={buildingId}
              onChange={(e) => setBuildingId(parseInt(e.target.value) || 1)}
              className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col items-center">
            <label className="text-xs font-medium text-gray-700 mb-1">F</label>
            <input
              type="number"
              min="1"
              value={floorId}
              onChange={(e) => setFloorId(parseInt(e.target.value) || 1)}
              className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col items-center">
            <label className="text-xs font-medium text-gray-700 mb-1">Z</label>
            <input
              type="number"
              min="1"
              value={zoneId}
              onChange={(e) => setZoneId(parseInt(e.target.value) || 1)}
              className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Right Panel - Floor Plan Editor */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 p-4">
          <div className="h-full flex flex-col">
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Floor Plan Editor</h3>
                  <p className="text-sm text-gray-600">Click on the floor plan to place markers</p>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-600">
                    Total Devices: <span className="font-semibold text-gray-800">{placedMarkers.length}</span>
                  </div>
                  <button
                    onClick={() => setPlacedMarkers([])}
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                     onClick={exportCoordinates}
                     className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                   >
                     Export JSON
                   </button>
                </div>
              </div>
            </div>
            
            <div className="flex-1 relative border-2 border-dashed border-gray-300 rounded-lg overflow-hidden bg-white">
              <img
                ref={imageRef}
                src="/plans/floorPlan-b1-f1.png"
                alt="Floor Plan"
                className="w-full h-full object-contain cursor-crosshair"
                onClick={handleImageClick}
                onMouseMove={handleImageMouseMove}
                onMouseLeave={handleImageMouseLeave}
              />
              
              {/* Render placed markers */}
              {placedMarkers.map((marker) => (
                <PlacedMarkerComponent
                  key={marker.id}
                  marker={marker}
                  onRemove={() => removeMarker(marker.id)}
                />
              ))}
            </div>
            
            {/* Bottom Section - Coordinates and Export */}
             <div className="mt-4 flex justify-between items-center">
               <CoordinateDisplay
                 currentCoordinates={currentCoordinates}
                 placedMarkers={placedMarkers}
                 onRemoveMarker={removeMarker}
                 onExport={exportCoordinates}
                 onClear={clearAllMarkers}
               />
             </div>
          </div>
        </div>
      </div>
    </div>
  );
}