'use client';

import React, { useState } from 'react';
import { PlacedMarker } from './types';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

interface PlacedMarkerComponentProps {
  marker: PlacedMarker;
  onRemove: () => void;
}

export function PlacedMarkerComponent({ marker, onRemove }: PlacedMarkerComponentProps) {
  const [showTooltip, setShowTooltip] = useState(false);

  const getMarkerSvgIcon = (type: string) => {
    switch (type) {
      case 'door': return 'door';
      case 'camera': return 'camera';
      case 'fire': return 'fire';
      case 'gate': return 'gateBarrier';
      default: return 'door';
    }
  };

  const getStatusStyles = (status: string, isAlert: boolean) => {
    if (isAlert) {
      return {
        bg: 'bg-red-100',
        border: 'border-red-500',
        text: 'text-red-700',
        iconColor: '#dc2626'
      };
    }

    switch (status) {
      case 'normal':
        return {
          bg: 'bg-green-100',
          border: 'border-green-500',
          text: 'text-green-700',
          iconColor: '#16a34a'
        };
      case 'open':
        return {
          bg: 'bg-blue-100',
          border: 'border-blue-500',
          text: 'text-blue-700',
          iconColor: '#2563eb'
        };
      case 'closed':
        return {
          bg: 'bg-gray-100',
          border: 'border-gray-500',
          text: 'text-gray-700',
          iconColor: '#6b7280'
        };
      case 'recording':
        return {
          bg: 'bg-green-100',
          border: 'border-green-500',
          text: 'text-green-700',
          iconColor: '#16a34a'
        };
      default:
        return {
          bg: 'bg-gray-100',
          border: 'border-gray-500',
          text: 'text-gray-700',
          iconColor: '#6b7280'
        };
    }
  };

  const styles = getStatusStyles(marker.status, marker.isAlert);

  return (
    <div
      className="absolute transform -translate-x-1/2 -translate-y-1/2 z-10 group"
      style={{
        left: `${marker.position_x}%`,
        top: `${marker.position_y}%`
      }}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {/* Main marker icon */}
      <div
        className={`w-12 h-12 rounded-full border-2 ${styles.bg} ${styles.border} flex items-center justify-center cursor-pointer hover:scale-110 transition-transform duration-200`}
      >
        <SvgIcon 
          name={getMarkerSvgIcon(marker.type)} 
          size="lg" 
          strokeColor={styles.iconColor}
        />
      </div>

      {/* Small remove button - appears on hover */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onRemove();
        }}
        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-xs font-bold shadow-lg"
        title="Remove marker"
      >
        ×
      </button>

      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20">
          <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap shadow-lg">
            <div className="font-semibold">{marker.name}</div>
            <div className="text-gray-300">Status: {marker.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
            <div className="text-gray-400 text-xs mt-1">
              Position: {Math.round(marker.exact_x)}px, {Math.round(marker.exact_y)}px
            </div>
            <div className="text-gray-400 text-xs">
              Building: {marker.buildingId} | Floor: {marker.floorId}
            </div>
            
            {/* Tooltip arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="border-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        </div>
      )}

      {/* Alert pulse animation for alert markers */}
      {marker.isAlert && (
        <div className="absolute inset-0 rounded-full border-2 border-red-400 animate-ping opacity-75"></div>
      )}
    </div>
  );
}