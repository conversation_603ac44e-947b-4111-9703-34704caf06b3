export type MarkerType = 'door' | 'camera' | 'fire' | 'gate';

export type MarkerStatus = 'normal' | 'open' | 'closed' | 'locked' | 'recording' | 'offline' | 'motion_detected' | 'smoke_detected' | 'alarm_triggered' | 'maintenance_required';

export interface PlacedMarker {
  id: string;
  name: string;
  type: MarkerType;
  status: MarkerStatus;
  position_x: number; // percentage x coordinate
  position_y: number; // percentage y coordinate
  exact_x: number; // exact pixel x coordinate
  exact_y: number; // exact pixel y coordinate
  isAlert: boolean;
  floorId: string;
  zoneId: string;
  buildingId: string;
  description: string;
  lastUpdated: string;
  metadata: {
    placedBy: string;
    placedAt: string;
  };
}

export interface MarkerConfig {
  type: MarkerType;
  status: MarkerStatus;
  label: string;
  svgIcon: string; // SVG icon name from public/svg
  description: string;
}

export interface MarkerStatusConfig {
  status: MarkerStatus;
  label: string;
  color: string;
  bgColor: string;
  description: string;
  isAlert: boolean;
}