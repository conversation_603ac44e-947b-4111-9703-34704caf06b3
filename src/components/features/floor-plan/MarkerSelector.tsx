'use client';

import React from 'react';
import { MarkerType, MarkerConfig } from './types';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

const MARKER_CONFIGS: MarkerConfig[] = [
  {
    type: 'door',
    status: 'normal',
    label: 'Door',
    svgIcon: 'door',
    description: 'Door access point'
  },
  {
    type: 'camera',
    status: 'recording',
    label: 'Camera',
    svgIcon: 'camera',
    description: 'Security camera'
  },
  {
    type: 'fire',
    status: 'normal',
    label: 'Fire Detector',
    svgIcon: 'fire',
    description: 'Fire detection system'
  },
  {
    type: 'gate',
    status: 'closed',
    label: 'Gate',
    svgIcon: 'gateBarrier',
    description: 'Security gate'
  }
];



interface MarkerSelectorProps {
  selectedMarkerType: MarkerType;
  onMarkerTypeChange: (type: MarkerType) => void;
}

export function MarkerSelector({
  selectedMarkerType,
  onMarkerTypeChange
}: MarkerSelectorProps) {
  return (
    <div className="space-y-3">
      {/* Compact Icon Selection */}
      <div className="flex flex-col space-y-2">
        {MARKER_CONFIGS.map((config) => (
          <button
            key={config.type}
            onClick={() => onMarkerTypeChange(config.type)}
            className={`w-12 h-12 rounded-lg border-2 transition-all duration-200 flex items-center justify-center ${
              selectedMarkerType === config.type
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            title={`${config.label} - ${config.description}`}
          >
            <SvgIcon 
               name={config.svgIcon} 
               size="lg" 
               strokeColor={selectedMarkerType === config.type ? '#1d4ed8' : '#374151'}
             />
          </button>
        ))}
      </div>
    </div>
  );
}