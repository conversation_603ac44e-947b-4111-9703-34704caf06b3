import { Events, generateEventCode } from './types';

export const mockEvents: Events = {
  events: [
    {
      id: 1,
      eventCode: generateEventCode('fire', 1),
      deviceType: 'fire',
      name: 'Fire Detector FD-001 Alert',
      timestamp: '2024-01-15T10:30:00Z',
      status: 'alarm_triggered',
      isAlert: true
    },
    {
      id: 2,
      eventCode: generateEventCode('fire', 2),
      deviceType: 'fire',
      name: 'Fire Detector FD-002 Status',
      timestamp: '2024-01-15T08:15:00Z',
      status: 'normal',
      isAlert: false
    },
    {
      id: 3,
      eventCode: generateEventCode('door', 1),
      deviceType: 'door',
      name: 'Main Entrance Access',
      timestamp: '2024-01-15T10:25:00Z',
      status: 'Opened',
      isAlert: true
    },
    {
      id: 4,
      eventCode: generateEventCode('door', 3),
      deviceType: 'door',
      name: 'Executive Office Door',
      timestamp: '2024-01-15T09:15:00Z',
      status: 'Closed',
      isAlert: false
    },
    {
      id: 5,
      eventCode: generateEventCode('door', 4),
      deviceType: 'door',
      name: 'Conference Room Access Denied',
      timestamp: '2024-01-15T11:05:00Z',
      status: 'locked',
      isAlert: true
    },
    {
      id: 6,
      eventCode: generateEventCode('camera', 3),
      deviceType: 'camera',
      name: 'Lobby Security Camera',
      timestamp: '2024-01-15T10:10:00Z',
      status: 'recording',
      isAlert: false
    },
    {
      id: 7,
      eventCode: generateEventCode('camera', 4),
      deviceType: 'camera',
      name: 'Executive Area Camera Offline',
      timestamp: '2024-01-15T11:15:00Z',
      status: 'offline',
      isAlert: true
    },
    {
      id: 8,
      eventCode: generateEventCode('camera', 5),
      deviceType: 'camera',
      name: 'Conference Room Motion Detection',
      timestamp: '2024-01-15T11:20:00Z',
      status: 'Active Incidents',
      isAlert: true
    },
    {
      id: 9,
      eventCode: generateEventCode('gate', 3),
      deviceType: 'gate',
      name: 'Parking Gate Access',
      timestamp: '2024-01-15T08:30:00Z',
      status: 'open',
      isAlert: false
    },
    {
      id: 10,
      eventCode: generateEventCode('door', 5),
      deviceType: 'door',
      name: 'Lobby Access Door',
      timestamp: '2024-01-15T10:40:00Z',
      status: 'Opened',
      isAlert: true
    },
    {
      id: 11,
      eventCode: generateEventCode('fire', 3),
      deviceType: 'fire',
      name: 'Fire Detector FD-003 Status',
      timestamp: '2024-01-15T09:45:00Z',
      status: 'normal',
      isAlert: false
    },
    {
      id: 12,
      eventCode: generateEventCode('gate', 4),
      deviceType: 'gate',
      name: 'Security Turnstile',
      timestamp: '2024-01-15T08:45:00Z',
      status: 'closed',
      isAlert: false
    },
    {
      id: 13,
      eventCode: generateEventCode('door', 2),
      deviceType: 'door',
      name: 'Operations Center Breach Attempt',
      timestamp: '2024-01-15T10:45:00Z',
      status: 'locked',
      isAlert: true
    },
    {
      id: 14,
      eventCode: generateEventCode('gate', 1),
      deviceType: 'gate',
      name: 'Security Gate Maintenance Alert',
      timestamp: '2024-01-15T09:45:00Z',
      status: 'Unauthorized attempts',
      isAlert: true
    },
    {
      id: 15,
      eventCode: generateEventCode('camera', 1),
      deviceType: 'camera',
      name: 'Security Camera CAM-001',
      timestamp: '2024-01-15T10:20:00Z',
      status: 'recording',
      isAlert: false
    },
    {
      id: 16,
      eventCode: generateEventCode('camera', 2),
      deviceType: 'camera',
      name: 'Storage Area Motion Alert',
      timestamp: '2024-01-15T10:50:00Z',
      status: 'Active Incidents',
      isAlert: true
    },
    {
      id: 17,
      eventCode: generateEventCode('camera', 6),
      deviceType: 'camera',
      name: 'Operations Camera CAM-006',
      timestamp: '2024-01-15T10:35:00Z',
      status: 'recording',
      isAlert: false
    },
    {
      id: 18,
      eventCode: generateEventCode('door', 6),
      deviceType: 'door',
      name: 'Laboratory Security Breach',
      timestamp: '2024-01-15T10:55:00Z',
      status: 'locked',
      isAlert: true
    },
    {
      id: 19,
      eventCode: generateEventCode('fire', 4),
      deviceType: 'fire',
      name: 'Fire Detector FD-004 Status',
      timestamp: '2024-01-15T10:00:00Z',
      status: 'normal',
      isAlert: false
    },
    {
      id: 20,
      eventCode: generateEventCode('camera', 7),
      deviceType: 'camera',
      name: 'Development Area Camera Failure',
      timestamp: '2024-01-15T09:30:00Z',
      status: 'offline',
      isAlert: true
    },
    {
      id: 21,
      eventCode: generateEventCode('gate', 5),
      deviceType: 'gate',
      name: 'Control Room Gate Access',
      timestamp: '2024-01-15T08:00:00Z',
      status: 'open',
      isAlert: false
    },
    {
      id: 22,
      eventCode: generateEventCode('fire', 5),
      deviceType: 'fire',
      name: 'Fire Alarm System Emergency',
      timestamp: '2024-01-15T10:58:00Z',
      status: 'alarm_triggered',
      isAlert: true
    },
    {
      id: 23,
      eventCode: generateEventCode('gate', 6),
      deviceType: 'gate',
      name: 'Perimeter Gate Breach Attempt',
      timestamp: '2024-01-15T11:30:00Z',
      status: 'Unauthorized attempts',
      isAlert: true
    },
    {
      id: 24,
      eventCode: generateEventCode('camera', 8),
      deviceType: 'camera',
      name: 'Parking Lot Surveillance',
      timestamp: '2024-01-15T09:00:00Z',
      status: 'Active Incidents',
      isAlert: true
    },
    {
      id: 25,
      eventCode: generateEventCode('door', 7),
      deviceType: 'door',
      name: 'Emergency Exit Door',
      timestamp: '2024-01-15T11:45:00Z',
      status: 'Closed',
      isAlert: false
    }
  ]
};