import { Markers } from './types';
import { getMarkerZoneFormat } from './zone-formatter';

export const mockMarkers: Markers = {
  markers: [
    {
      id: 1,
      name: 'fire-001',
      type: 'fire',
      position_x: 150,
      position_y: 200,
      status: 'smoke_detected',
      isAlert: true,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Fire Detector FD-001',
      subtitle: 'Smoke Level: Critical',
      description: 'Photoelectric smoke detector in north wing corridor',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
      alertTimestamp: '2024-01-15T10:30:00Z'
    },
    {
      id: 2,
      name: 'fire-002',
      type: 'fire',
      position_x: 450,
      position_y: 180,
      status: 'normal',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Fire Detector FD-002',
      subtitle: 'Status: Normal',
      description: 'Heat detector in north wing conference room',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
      alertTimestamp: '2024-01-15T08:15:00Z'
    },
    {
      id: 3,
      name: 'door-001',
      type: 'door',
      position_x: 300,
      position_y: 150,
      status: 'open',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Main Entrance',
      subtitle: 'Access Granted',
      description: 'Main entrance door with card reader access',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
      alertTimestamp: '2024-01-15T10:25:00Z'
    },
    {
      id: 4,
      name: 'door-003',
      type: 'door',
      position_x: 150,
      position_y: 100,
      status: 'closed',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Executive Office Door',
      subtitle: 'Secure Access',
      description: 'Executive office door with keycard access',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
      alertTimestamp: '2024-01-15T09:15:00Z'
    },
    {
      id: 5,
      name: 'door-004',
      type: 'door',
      position_x: 450,
      position_y: 120,
      status: 'locked',
      isAlert: true,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Conference Room Door',
      subtitle: 'Access Denied',
      description: 'Conference room door with biometric scanner',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
      alertTimestamp: '2024-01-15T11:05:00Z'
    },
    {
      id: 6,
      name: 'camera-003',
      type: 'camera',
      position_x: 200,
      position_y: 200,
      status: 'recording',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Lobby Security Camera',
      subtitle: 'HD Recording Active',
      description: 'Fixed security camera monitoring main lobby area',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
      alertTimestamp: '2024-01-15T10:10:00Z'
    },
    {
      id: 7,
      name: 'camera-004',
      type: 'camera',
      position_x: 100,
      position_y: 80,
      status: 'offline',
      isAlert: true,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Executive Area Camera',
      subtitle: 'Connection Lost',
      description: 'PTZ camera covering executive office area',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
      alertTimestamp: '2024-01-15T11:15:00Z'
    },
    {
      id: 8,
      name: 'camera-005',
      type: 'camera',
      position_x: 380,
      position_y: 180,
      status: 'motion_detected',
      isAlert: true,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Conference Room Camera',
      subtitle: 'Motion Alert',
      description: 'Conference room surveillance camera with motion detection',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
      alertTimestamp: '2024-01-15T11:20:00Z'
    },
    {
      id: 9,
      name: 'gate-003',
      type: 'gate',
      position_x: 350,
      position_y: 50,
      status: 'open',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Parking Gate PG-001',
      subtitle: 'Access Granted',
      description: 'Automated parking barrier gate',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Parking Entrance',
      alertTimestamp: '2024-01-15T08:30:00Z'
    },
    {
      id: 10,
      name: 'door-005',
      type: 'door',
      position_x: 250,
      position_y: 180,
      status: 'open',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Lobby Access Door',
      subtitle: 'Access Granted',
      description: 'Automatic sliding door with proximity sensor in main lobby',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
      alertTimestamp: '2024-01-15T10:40:00Z'
    },
    {
      id: 11,
      name: 'fire-003',
      type: 'fire',
      position_x: 180,
      position_y: 120,
      status: 'normal',
      isAlert: false,
      floorId: 1, // Building A - North Wing - Ground Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Fire Detector FD-003',
      subtitle: 'Status: Normal',
      description: 'Combination smoke and heat detector in executive area',
      zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
      publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
      alertTimestamp: '2024-01-15T09:45:00Z'
    },
    {
       id: 12,
       name: 'gate-004',
       type: 'gate',
       position_x: 320,
       position_y: 220,
       status: 'closed',
       isAlert: false,
       floorId: 1, // Building A - North Wing - Ground Floor
       zoneId: 1, // North Wing
       buildingId: 1, // Main Corporate Building
       title: 'Security Turnstile GT-001',
       subtitle: 'Access Control Active',
       description: 'Electronic turnstile gate for lobby access control',
       zone: getMarkerZoneFormat(1, 1), // Floor 1, Building A, North Wing
       publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
       alertTimestamp: '2024-01-15T08:45:00Z'
     },
     {
      id: 13,
      name: 'door-002',
      type: 'door',
      position_x: 500,
      position_y: 300,
      status: 'locked',
      isAlert: true,
      floorId: 3, // Building A - South Wing - Ground Floor
      zoneId: 2, // South Wing
      buildingId: 1, // Main Corporate Building
      title: 'Operations Center Access',
      subtitle: 'Unauthorized Attempt',
      description: 'High-security door to operations center with biometric access',
      zone: getMarkerZoneFormat(3, 1), // Floor 1, Building A, South Wing
      publicAddress: 'Main Corporate Building, Ground Floor, South Wing, Operations Center',
      alertTimestamp: '2024-01-15T10:45:00Z'
    },
    {
      id: 14,
      name: 'gate-001',
      type: 'gate',
      position_x: 250,
      position_y: 350,
      status: 'maintenance_required',
      isAlert: true,
      floorId: 3, // Building A - South Wing - Ground Floor
      zoneId: 2, // South Wing
      buildingId: 1, // Main Corporate Building
      title: 'Security Gate SG-001',
      subtitle: 'Maintenance Required',
      description: 'Automated security barrier at south entrance',
      zone: getMarkerZoneFormat(3, 1), // Floor 1, Building A, South Wing
      publicAddress: 'Main Corporate Building, Ground Floor, South Wing, Utility Room',
      alertTimestamp: '2024-01-15T09:45:00Z'
    },
    {
      id: 15,
      name: 'camera-001',
      type: 'camera',
      position_x: 400,
      position_y: 100,
      status: 'recording',
      isAlert: false,
      floorId: 2, // Building A - North Wing - Second Floor
      zoneId: 1, // North Wing
      buildingId: 1, // Main Corporate Building
      title: 'Security Camera CAM-001',
      subtitle: 'HD Recording Active',
      description: 'PTZ security camera with night vision capability',
      zone: getMarkerZoneFormat(2, 1), // Floor 2, Building A, North Wing
      publicAddress: 'Main Corporate Building, Second Floor, North Wing, Office 201',
      alertTimestamp: '2024-01-15T10:20:00Z'
    },
    {
      id: 16,
      name: 'camera-002',
      type: 'camera',
      position_x: 200,
      position_y: 400,
      status: 'motion_detected',
      isAlert: true,
      floorId: 5, // Building B - Storage Area Alpha - Ground Floor
      zoneId: 3, // Storage Area Alpha
      buildingId: 2, // Warehouse Complex
      title: 'Security Camera CAM-002',
      subtitle: 'Motion Alert',
      description: 'Fixed security camera monitoring storage area',
      zone: getMarkerZoneFormat(5, 2), // Floor 1, Building B, Storage Area Alpha
      publicAddress: 'Warehouse Complex, Ground Floor, Storage Area Alpha, Storage Bay Alpha-1',
      alertTimestamp: '2024-01-15T10:50:00Z'
    },
    {
      id: 17,
      name: 'camera-006',
      type: 'camera',
      position_x: 180,
      position_y: 280,
      status: 'recording',
      isAlert: false,
      floorId: 6, // Building B - Office Wing - Second Floor
      zoneId: 4, // Office Wing
      buildingId: 2, // Warehouse Complex
      title: 'Operations Camera CAM-006',
      subtitle: 'HD Recording Active',
      description: 'High-definition security camera monitoring warehouse operations center',
      zone: getMarkerZoneFormat(6, 2), // Floor 2, Building B, Office Wing
      publicAddress: 'Warehouse Complex, Second Floor, Office Wing, Warehouse Operations Center',
      alertTimestamp: '2024-01-15T10:35:00Z'
    },
    {
      id: 18,
      name: 'door-006',
      type: 'door',
      position_x: 350,
      position_y: 250,
      status: 'locked',
      isAlert: true,
      floorId: 7, // Building C - Laboratory Section - Ground Floor
      zoneId: 5, // Laboratory Section
      buildingId: 3, // R&D Center
      title: 'Laboratory Access Door',
      subtitle: 'Security Breach Detected',
      description: 'High-security laboratory door with biometric access control',
      zone: getMarkerZoneFormat(7, 3), // Floor 1, Building C, Laboratory Section
      publicAddress: 'R&D Center, Ground Floor, Laboratory Section, Research Lab 1',
      alertTimestamp: '2024-01-15T10:55:00Z'
    },
    {
      id: 19,
      name: 'fire-004',
      type: 'fire',
      position_x: 320,
      position_y: 320,
      status: 'normal',
      isAlert: false,
      floorId: 8, // Building C - Laboratory Section - Second Floor
      zoneId: 5, // Laboratory Section
      buildingId: 3, // R&D Center
      title: 'Fire Detector FD-004',
      subtitle: 'Status: Normal',
      description: 'Advanced multi-sensor fire detector for laboratory environment',
      zone: getMarkerZoneFormat(8, 3), // Floor 2, Building C, Laboratory Section
      publicAddress: 'R&D Center, Second Floor, Laboratory Section, Testing Lab',
      alertTimestamp: '2024-01-15T10:00:00Z'
    },
    {
      id: 20,
      name: 'camera-007',
      type: 'camera',
      position_x: 480,
      position_y: 220,
      status: 'offline',
      isAlert: true,
      floorId: 9, // Building C - Development Area - Third Floor
      zoneId: 6, // Development Area
      buildingId: 3, // R&D Center
      title: 'Development Area Camera',
      subtitle: 'Connection Lost',
      description: 'Network security camera with remote monitoring capability',
      zone: getMarkerZoneFormat(9, 3), // Floor 3, Building C, Development Area
      publicAddress: 'R&D Center, Third Floor, Development Area, Development Workshop',
      alertTimestamp: '2024-01-15T09:30:00Z'
    },
    {
      id: 21,
      name: 'gate-005',
      type: 'gate',
      position_x: 120,
      position_y: 120,
      status: 'open',
      isAlert: false,
      floorId: 10, // Building D - Control Room - Ground Floor
      zoneId: 7, // Control Room
      buildingId: 4, // Security Operations Center
      title: 'Control Room Gate',
      subtitle: 'Access Granted',
      description: 'Automated security gate for control room access',
      zone: getMarkerZoneFormat(10, 4), // Floor 1, Building D, Control Room
      publicAddress: 'Security Operations Center, Ground Floor, Control Room, Main Control Room',
      alertTimestamp: '2024-01-15T08:00:00Z'
    },
    {
      id: 22,
      name: 'fire-005',
      type: 'fire',
      position_x: 380,
      position_y: 380,
      status: 'alarm_triggered',
      isAlert: true,
      floorId: 12, // Building E - Reception Area - Ground Floor
      zoneId: 8, // Reception Area
      buildingId: 5, // Guest House
      title: 'Fire Alarm System FAS-001',
      subtitle: 'Fire Alarm Active',
      description: 'Integrated fire detection and alarm system',
      zone: getMarkerZoneFormat(12, 5), // Floor 1, Building E, Reception Area
      publicAddress: 'Guest House, Ground Floor, Reception Area, Reception Desk',
      alertTimestamp: '2024-01-15T10:58:00Z'
    }
  ]
};